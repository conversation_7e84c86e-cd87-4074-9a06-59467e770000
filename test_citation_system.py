#!/usr/bin/env python3
"""
Test script for the Citation RAG System
"""

import os
import json
import tempfile
from pathlib import Path

# Create a simple test PDF content (text-based for testing)
def create_test_pdf_content():
    """Create a simple test document for demonstration"""
    test_content = """
    # Test Document: AI and Machine Learning

    ## Introduction
    Artificial Intelligence (AI) has revolutionized many industries. Machine learning algorithms can process vast amounts of data to identify patterns and make predictions.

    ## Key Concepts
    Deep learning is a subset of machine learning that uses neural networks with multiple layers. These networks can learn complex representations of data.

    ## Applications
    AI applications include natural language processing, computer vision, and robotics. These technologies are transforming healthcare, finance, and transportation.

    ## Future Directions
    The future of AI includes developments in explainable AI, quantum computing integration, and ethical AI frameworks.
    """

    # Save as a text file for testing (since we don't have a real PDF)
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        return f.name

def test_document_processor():
    """Test the document processing functionality"""
    print("Testing Document Processor...")

    from citation_system import DocumentProcessor

    processor = DocumentProcessor()

    # Test sentence parsing
    test_text = "This is the first sentence. This is the second sentence. And this is the third sentence."
    sentences = processor.parse_chunk_into_sentences(test_text)

    print(f"Parsed {len(sentences)} sentences:")
    for sentence in sentences:
        print(f"  {sentence['sentence_id']}: {sentence['text']}")

    print("✅ Document Processor test passed\n")

def test_citation_parser():
    """Test the citation parsing functionality"""
    print("Testing Citation Parser...")

    from citation_system import CitationParser

    parser = CitationParser()

    # Mock response with citations
    test_response = """AI has many applications. <CIT chunk_id='0' sentences='1-2'>Machine learning can process vast amounts of data</CIT> and <CIT chunk_id='1' sentences='3-4'>neural networks learn complex representations</CIT>."""

    # Mock chunks data
    mock_chunks = [
        {
            'document_id': 'test_doc_1',
            'page_number': 1,
            'sentences_data': [
                {'sentence_id': 1, 'text': 'Machine learning algorithms can process vast amounts of data.', 'start_char': 0, 'end_char': 60},
                {'sentence_id': 2, 'text': 'They identify patterns and make predictions.', 'start_char': 61, 'end_char': 104}
            ]
        },
        {
            'document_id': 'test_doc_1',
            'page_number': 2,
            'sentences_data': [
                {'sentence_id': 3, 'text': 'Neural networks have multiple layers.', 'start_char': 0, 'end_char': 37},
                {'sentence_id': 4, 'text': 'They learn complex representations of data.', 'start_char': 38, 'end_char': 81}
            ]
        }
    ]

    result = parser.parse_response_with_citations(test_response, mock_chunks)

    print(f"Parsed text: {result['text']}")
    print(f"Found {len(result['citations'])} citations:")
    for i, citation in enumerate(result['citations']):
        print(f"  Citation {i+1}: {citation['answer_snippet']} -> Page {citation['page_number']}")

    print("✅ Citation Parser test passed\n")

def test_sqlite3_availability():
    """Test if SQLite3 is available (it should be built into Python)"""
    print("Testing SQLite3 Availability...")

    try:
        import sqlite3
        print(f"✅ SQLite3 is available")
        print(f"   SQLite version: {sqlite3.sqlite_version}")
        print(f"   Python sqlite3 module version: {sqlite3.version}")

        # Test basic functionality
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        cursor.execute('CREATE TABLE test (id INTEGER, name TEXT)')
        cursor.execute('INSERT INTO test VALUES (1, "test")')
        cursor.execute('SELECT * FROM test')
        result = cursor.fetchone()
        conn.close()

        if result == (1, "test"):
            print("✅ SQLite3 basic functionality works")
            return True
        else:
            print("❌ SQLite3 basic functionality failed")
            return False

    except ImportError:
        print("❌ SQLite3 is not available (this should never happen in standard Python)")
        return False
    except Exception as e:
        print(f"❌ SQLite3 error: {e}")
        return False

def test_environment_setup():
    """Test if environment variables are set up correctly"""
    print("Testing Environment Setup...")

    required_vars = ['OPENAI_API_KEY', 'KDBAI_ENDPOINT', 'KDBAI_API_KEY']
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file or environment")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def main():
    """Run all tests"""
    print("🧪 Testing Citation RAG System\n")
    print("=" * 50)

    # Test SQLite3 first (should always work)
    sqlite_ok = test_sqlite3_availability()

    # Test individual components
    test_document_processor()
    test_citation_parser()

    # Test environment setup
    env_ok = test_environment_setup()

    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   SQLite3: {'✅ Working' if sqlite_ok else '❌ Failed'}")
    print(f"   Environment: {'✅ Ready' if env_ok else '❌ Missing API keys'}")

    if sqlite_ok and env_ok:
        print("\n🎉 All tests passed! The system should work correctly.")
        print("\nTo run the web interface:")
        print("  python web_interface.py")
        print("\nThen visit: http://localhost:8000")
    elif sqlite_ok and not env_ok:
        print("\n⚠️  SQLite3 works, but please fix environment setup.")
        print("Copy .env.example to .env and fill in your API keys.")
        print("You can still run the demo: python demo_citation_parsing.py")
    else:
        print("\n❌ Critical error: SQLite3 not working properly.")
        print("This indicates a problem with your Python installation.")

if __name__ == "__main__":
    main()
