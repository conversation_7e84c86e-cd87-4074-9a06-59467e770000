#!/usr/bin/env python3
"""
Diagnostic script to identify the root cause of the upload error
"""

import os
import sys
import traceback
from pathlib import Path

def test_environment_variables():
    """Test if required environment variables are set"""
    print("🔍 Testing Environment Variables...")
    
    required_vars = ['OPENAI_API_KEY', 'KDBAI_ENDPOINT', 'KDBAI_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
            print(f"❌ {var}: Not set")
        else:
            # Show partial value for security
            masked_value = value[:8] + "..." if len(value) > 8 else "***"
            print(f"✅ {var}: {masked_value}")
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All environment variables are set")
        return True

def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n📦 Testing Dependencies...")
    
    dependencies = [
        ('PyPDF2', 'PyPDF2'),
        ('kdbai_client', 'kdbai_client'),
        ('sentence_transformers', 'sentence_transformers'),
        ('openai', 'openai'),
        ('pandas', 'pandas'),
        ('numpy', 'numpy'),
        ('dotenv', 'python-dotenv')
    ]
    
    missing_deps = []
    
    for module_name, package_name in dependencies:
        try:
            __import__(module_name)
            print(f"✅ {package_name}: Available")
        except ImportError as e:
            print(f"❌ {package_name}: Missing ({e})")
            missing_deps.append(package_name)
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("Run: pip install -r requirements.txt")
        return False
    else:
        print("✅ All dependencies are available")
        return True

def test_citation_system_initialization():
    """Test CitationRAGSystem initialization step by step"""
    print("\n🏗️ Testing CitationRAGSystem Initialization...")
    
    try:
        # Load environment variables first
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
        
        # Test individual components
        print("\n1. Testing DocumentProcessor...")
        from citation_system import DocumentProcessor
        doc_processor = DocumentProcessor()
        print("✅ DocumentProcessor initialized")
        
        print("\n2. Testing KDBAIVectorStore...")
        try:
            from citation_system import KDBAIVectorStore
            vector_store = KDBAIVectorStore()
            print("✅ KDBAIVectorStore initialized")
        except Exception as e:
            print(f"❌ KDBAIVectorStore failed: {e}")
            print("   This is likely due to missing KDB.AI credentials")
            return False
        
        print("\n3. Testing CitationGenerator...")
        try:
            from citation_system import CitationGenerator
            citation_gen = CitationGenerator()
            print("✅ CitationGenerator initialized")
        except Exception as e:
            print(f"❌ CitationGenerator failed: {e}")
            print("   This is likely due to missing OpenAI API key")
            return False
        
        print("\n4. Testing CitationParser...")
        from citation_system import CitationParser
        citation_parser = CitationParser()
        print("✅ CitationParser initialized")
        
        print("\n5. Testing full CitationRAGSystem...")
        from citation_system import CitationRAGSystem
        rag_system = CitationRAGSystem()
        print("✅ CitationRAGSystem initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ CitationRAGSystem initialization failed: {e}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

def test_pdf_processing():
    """Test PDF processing with a simple test file"""
    print("\n📄 Testing PDF Processing...")
    
    try:
        from citation_system import DocumentProcessor
        processor = DocumentProcessor()
        
        # Create a simple test PDF content (we'll use text for now)
        test_text = "This is a test document. It contains multiple sentences. Each sentence should be parsed correctly."
        
        # Test sentence parsing
        sentences = processor.parse_chunk_into_sentences(test_text)
        print(f"✅ Sentence parsing works: {len(sentences)} sentences found")
        
        for i, sentence in enumerate(sentences[:3]):  # Show first 3
            print(f"   Sentence {sentence['sentence_id']}: {sentence['text']}")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF processing test failed: {e}")
        traceback.print_exc()
        return False

def test_sqlite_database():
    """Test SQLite database functionality"""
    print("\n🗄️ Testing SQLite Database...")
    
    try:
        import sqlite3
        import tempfile
        import json
        
        # Test database creation
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_db:
            db_path = tmp_db.name
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create test table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                total_pages INTEGER,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Test insert
        test_metadata = {"title": "Test Document", "author": "Test Author"}
        cursor.execute('''
            INSERT INTO documents (id, filename, file_path, total_pages, metadata)
            VALUES (?, ?, ?, ?, ?)
        ''', ("test_doc", "test.pdf", "/tmp/test.pdf", 5, json.dumps(test_metadata)))
        
        conn.commit()
        
        # Test query
        cursor.execute("SELECT * FROM documents WHERE id = ?", ("test_doc",))
        result = cursor.fetchone()
        
        conn.close()
        os.unlink(db_path)
        
        if result:
            print("✅ SQLite database operations work correctly")
            return True
        else:
            print("❌ SQLite database query failed")
            return False
            
    except Exception as e:
        print(f"❌ SQLite database test failed: {e}")
        traceback.print_exc()
        return False

def test_web_interface_health():
    """Test if the web interface can be imported and health endpoint works"""
    print("\n🌐 Testing Web Interface Health...")
    
    try:
        # Test import
        from web_interface import app, rag_system
        print("✅ Web interface imports successfully")
        
        # Check RAG system status
        if rag_system:
            print("✅ RAG system is initialized in web interface")
            return True
        else:
            print("❌ RAG system is None in web interface")
            print("   This means initialization failed during web interface startup")
            return False
            
    except Exception as e:
        print(f"❌ Web interface test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive diagnostics"""
    print("🔍 Citation RAG System Upload Error Diagnostics")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Dependencies", test_dependencies),
        ("SQLite Database", test_sqlite_database),
        ("PDF Processing", test_pdf_processing),
        ("Citation System", test_citation_system_initialization),
        ("Web Interface", test_web_interface_health)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Diagnostic Summary:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    
    if all_passed:
        print("🎉 All diagnostics passed!")
        print("The upload error might be due to a specific PDF file or runtime issue.")
        print("Try uploading a different PDF file or check server logs for more details.")
    else:
        print("❌ Some diagnostics failed!")
        print("\n🔧 Recommended fixes:")
        
        if not results.get("Environment Variables", True):
            print("1. Set up your .env file with API keys:")
            print("   cp .env.example .env")
            print("   # Edit .env with your actual API keys")
        
        if not results.get("Dependencies", True):
            print("2. Install missing dependencies:")
            print("   pip install -r requirements.txt")
        
        if not results.get("Citation System", True):
            print("3. Check API credentials and KDB.AI setup")
        
        print("\n4. After fixing issues, restart the web interface:")
        print("   python web_interface.py")

if __name__ == "__main__":
    main()
