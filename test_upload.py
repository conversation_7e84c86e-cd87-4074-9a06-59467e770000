#!/usr/bin/env python3
"""
Test script to upload a PDF file to the Citation RAG System
"""

import requests
import os
import time

def test_upload():
    """Test uploading a PDF file"""
    print("🧪 Testing PDF Upload to Citation RAG System")
    print("=" * 50)
    
    # Check if test PDF exists
    pdf_file = "test_document.pdf"
    if not os.path.exists(pdf_file):
        print(f"❌ Test PDF file not found: {pdf_file}")
        print("Run: python create_test_pdf.py")
        return False
    
    print(f"📄 Found test PDF: {pdf_file}")
    print(f"📊 File size: {os.path.getsize(pdf_file)} bytes")
    
    # Test server health first
    try:
        print("\n🔍 Testing server health...")
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ Server is healthy")
            print(f"   RAG system initialized: {health_data.get('rag_system_initialized', 'unknown')}")
        else:
            print(f"❌ Server health check failed: {health_response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        print("Make sure the server is running: python web_interface.py")
        return False
    
    # Test file upload
    try:
        print(f"\n📤 Uploading PDF file: {pdf_file}")
        
        with open(pdf_file, 'rb') as f:
            files = {'file': (pdf_file, f, 'application/pdf')}
            
            print("🔄 Sending upload request...")
            response = requests.post(
                "http://localhost:8000/upload",
                files=files,
                timeout=60  # Give it time to process
            )
        
        print(f"📨 Response status: {response.status_code}")
        print(f"📨 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Upload successful!")
            print(f"   Status: {result.get('status')}")
            print(f"   Document ID: {result.get('document_id')}")
            print(f"   Filename: {result.get('filename')}")
            print(f"   Message: {result.get('message')}")
            return True
        else:
            print(f"❌ Upload failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error detail: {error_data.get('detail', 'No detail provided')}")
            except:
                print(f"   Raw response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Upload request timed out")
        print("   The server might be processing the file - check server logs")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Upload request failed: {e}")
        return False

def test_query_after_upload():
    """Test querying after upload"""
    print("\n🔍 Testing Query After Upload...")
    
    try:
        query_data = {"query": "What is artificial intelligence?"}
        response = requests.post(
            "http://localhost:8000/query",
            data=query_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Query successful!")
            print(f"   Answer: {result.get('answer', 'No answer')[:100]}...")
            print(f"   Citations: {len(result.get('citations', []))} found")
            return True
        else:
            print(f"❌ Query failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error detail: {error_data.get('detail', 'No detail provided')}")
            except:
                print(f"   Raw response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Query request failed: {e}")
        return False

def main():
    """Run upload test"""
    print("🚀 Citation RAG System Upload Test")
    print("Make sure the web interface is running: python web_interface.py")
    print("Then check the server logs for detailed error information")
    print()
    
    # Wait a moment for user to start server if needed
    input("Press Enter when the server is running...")
    
    # Test upload
    upload_success = test_upload()
    
    if upload_success:
        # Test query
        query_success = test_query_after_upload()
        
        if query_success:
            print("\n🎉 All tests passed!")
            print("✅ Upload functionality is working correctly")
            print("✅ Query functionality is working correctly")
        else:
            print("\n⚠️ Upload worked but query failed")
            print("Check server logs for query-related errors")
    else:
        print("\n❌ Upload test failed")
        print("Check server logs for detailed error information")
        print("\nCommon issues to check:")
        print("1. API keys are set correctly in .env file")
        print("2. KDB.AI connection is working")
        print("3. All dependencies are installed")
        print("4. Server has sufficient memory/resources")

if __name__ == "__main__":
    main()
