#!/usr/bin/env python3
"""
Demo script showing how the citation parsing works
This can run without API keys to demonstrate the core citation functionality
"""

import re
import json
from typing import List, Dict, Any

def parse_chunk_into_sentences(chunk_text: str) -> List[Dict[str, Any]]:
    """
    Splits chunk_text into sentences with start/end offsets.
    Based on the Medium article implementation.
    """
    # Simple regex to split on periods while capturing them
    raw_parts = re.split(r'(\.)', chunk_text)
    
    # Combine text + punctuation
    combined = []
    for i in range(0, len(raw_parts), 2):
        text_part = raw_parts[i].strip()
        punct = ""
        if i+1 < len(raw_parts):
            punct = raw_parts[i+1]
        if text_part or punct:
            combined_text = (text_part + punct).strip()
            if combined_text:
                combined.append(combined_text)
    
    sentences = []
    offset = 0
    for s_id, s_txt in enumerate(combined, start=1):
        start_char = offset
        end_char = start_char + len(s_txt)
        sentences.append({
            "sentence_id": s_id,
            "text": s_txt,
            "start_char": start_char,
            "end_char": end_char
        })
        offset = end_char + 1  # assume space after each
    return sentences

def parse_response_with_citations(response_text: str, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Parse response to extract citations and create structured output
    Based on the Medium article implementation
    """
    pattern = re.compile(
        r'(.*?)<CIT\s+chunk_id=[\'"](\d+)[\'\"]\s+sentences=[\'"](\d+-\d+)[\'"]>(.*?)(?:</CIT>|(?=<CIT)|$)',
        re.DOTALL
    )
    
    final_text = ""
    citations = []
    idx = 0
    
    while True:
        match = pattern.search(response_text, idx)
        if not match:
            # Add leftover text
            leftover = response_text[idx:]
            final_text += leftover
            break
        
        text_before = match.group(1)
        chunk_id_str = match.group(2)
        sent_range = match.group(3)
        snippet = match.group(4)
        
        final_text += text_before
        
        start_in_answer = len(final_text)
        final_text += snippet
        end_in_answer = len(final_text)
        
        # Get chunk data
        chunk_idx = int(chunk_id_str)
        if chunk_idx < len(chunks):
            chunk_data = chunks[chunk_idx]
            sentences_data = chunk_data['sentences_data']
            
            # Parse sentence range
            try:
                start_sent, end_sent = map(int, sent_range.split("-"))
            except:
                start_sent, end_sent = 1, 1
            
            # Get relevant sentences
            relevant_sents = [s for s in sentences_data if start_sent <= s["sentence_id"] <= end_sent]
            
            if relevant_sents:
                combined_text = " ".join(s["text"] for s in relevant_sents)
                chunk_start_char = relevant_sents[0]["start_char"]
                chunk_end_char = relevant_sents[-1]["end_char"]
            else:
                combined_text = ""
                chunk_start_char = -1
                chunk_end_char = -1
            
            citation = {
                "chunk_id": chunk_idx,
                "chunk_document_id": chunk_data['document_id'],
                "page_number": chunk_data['page_number'],
                "sentences_range": sent_range,
                "answer_snippet": snippet,
                "answer_snippet_start": start_in_answer,
                "answer_snippet_end": end_in_answer,
                "chunk_sentences_text": combined_text,
                "chunk_sentences_start": chunk_start_char,
                "chunk_sentences_end": chunk_end_char
            }
            citations.append(citation)
        
        idx = match.end()
    
    return {
        "type": "text",
        "text": final_text,
        "citations": citations
    }

def create_demo_data():
    """Create demo data to show how the citation system works"""
    
    # Sample document chunks
    chunk_texts = [
        "Artificial intelligence has revolutionized many industries. Machine learning algorithms can process vast amounts of data to identify patterns. These systems are becoming increasingly sophisticated.",
        
        "Deep learning is a subset of machine learning that uses neural networks. These networks have multiple layers that can learn complex representations. The technology has enabled breakthroughs in computer vision and natural language processing.",
        
        "AI applications are widespread in modern society. Healthcare systems use AI for diagnosis and treatment planning. Financial institutions employ machine learning for fraud detection and risk assessment."
    ]
    
    # Process chunks into structured data
    chunks = []
    for i, text in enumerate(chunk_texts):
        sentences_data = parse_chunk_into_sentences(text)
        chunk = {
            'document_id': f'demo_doc_{i}',
            'page_number': i + 1,
            'text': text,
            'sentences_data': sentences_data
        }
        chunks.append(chunk)
    
    return chunks

def demo_citation_parsing():
    """Demonstrate the citation parsing functionality"""
    print("🔍 Citation Parsing Demo")
    print("=" * 50)
    
    # Create demo data
    chunks = create_demo_data()
    
    print("📄 Sample Document Chunks:")
    for i, chunk in enumerate(chunks):
        print(f"\n[Chunk {i}] Page {chunk['page_number']}:")
        print(f"  {chunk['text']}")
        print(f"  Sentences: {len(chunk['sentences_data'])}")
    
    # Sample LLM response with citations
    llm_response = """AI has transformed multiple sectors. <CIT chunk_id='0' sentences='1-2'>Machine learning algorithms can process vast amounts of data to identify patterns</CIT>, making them invaluable for analysis. In healthcare, <CIT chunk_id='2' sentences='2-3'>AI systems are used for diagnosis and treatment planning</CIT>. Additionally, <CIT chunk_id='1' sentences='2-3'>neural networks with multiple layers can learn complex representations</CIT>."""
    
    print(f"\n🤖 Sample LLM Response with Citations:")
    print(f"  {llm_response}")
    
    # Parse the response
    parsed_result = parse_response_with_citations(llm_response, chunks)
    
    print(f"\n✨ Parsed Result:")
    print(f"📝 Clean Answer Text:")
    print(f"  {parsed_result['text']}")
    
    print(f"\n📚 Extracted Citations ({len(parsed_result['citations'])}):")
    for i, citation in enumerate(parsed_result['citations'], 1):
        print(f"\n  [{i}] Citation:")
        print(f"      Answer Snippet: '{citation['answer_snippet']}'")
        print(f"      Source: Page {citation['page_number']}, Sentences {citation['sentences_range']}")
        print(f"      Original Text: '{citation['chunk_sentences_text']}'")
        print(f"      Position in Answer: {citation['answer_snippet_start']}-{citation['answer_snippet_end']}")

def demo_html_generation():
    """Show how to generate HTML with interactive citations"""
    print("\n🌐 HTML Generation Demo")
    print("=" * 30)
    
    chunks = create_demo_data()
    llm_response = """AI has many applications. <CIT chunk_id='0' sentences='1-2'>Machine learning algorithms can process vast amounts of data</CIT> effectively."""
    
    parsed_result = parse_response_with_citations(llm_response, chunks)
    
    # Generate HTML
    html = generate_citation_html(parsed_result)
    print("Generated HTML:")
    print(html)

def generate_citation_html(parsed_result: Dict[str, Any]) -> str:
    """Generate HTML with interactive citations"""
    text = parsed_result['text']
    citations = parsed_result['citations']
    
    # Replace citation snippets with HTML spans
    html_text = text
    for i, citation in enumerate(citations):
        snippet = citation['answer_snippet']
        tooltip_content = f"Page {citation['page_number']}, Sentences {citation['sentences_range']}: {citation['chunk_sentences_text']}"
        
        citation_html = f'<span class="citation" title="{tooltip_content}" data-citation="{i+1}">{snippet}</span>'
        html_text = html_text.replace(snippet, citation_html, 1)
    
    return f'<div class="answer">{html_text}</div>'

def main():
    """Run the citation parsing demo"""
    print("📚 Anthropic-Style Citation System Demo")
    print("This demo shows how the citation parsing works without requiring API keys\n")
    
    demo_citation_parsing()
    demo_html_generation()
    
    print(f"\n🎯 Key Features Demonstrated:")
    print(f"  ✅ Sentence-level document parsing")
    print(f"  ✅ Citation tag extraction from LLM responses")
    print(f"  ✅ Mapping citations back to source sentences")
    print(f"  ✅ Structured citation metadata")
    print(f"  ✅ HTML generation for interactive display")
    
    print(f"\n🚀 To run the full system with PDF upload and web interface:")
    print(f"  1. Set up your API keys in .env file")
    print(f"  2. Run: python run_citation_system.py")

if __name__ == "__main__":
    main()
