<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Citation RAG System</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .section h2 {
            color: #495057;
            margin-top: 0;
        }
        
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e6f3ff;
        }
        
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #e6ffe6;
        }
        
        input[type="file"] {
            display: none;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .query-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .result-container {
            margin-top: 20px;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .citation {
            position: relative;
            text-decoration: underline;
            text-decoration-style: dotted;
            cursor: help;
            color: #007bff;
            background-color: #e6f3ff;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .citation:hover {
            background-color: #cce7ff;
        }
        
        .tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
            max-width: 400px;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            display: none;
        }
        
        .tooltip::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid #333;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .sources {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        
        .source-item {
            margin-bottom: 10px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Citation RAG System</h1>
        <p style="text-align: center; color: #6c757d; margin-bottom: 30px;">
            Upload PDF documents and ask questions with Anthropic-style citations
        </p>
        
        <!-- Document Upload Section -->
        <div class="section">
            <h2>📄 Upload Documents</h2>
            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                <div>
                    <strong>Click to upload PDF documents</strong><br>
                    <span style="color: #6c757d;">or drag and drop files here</span>
                </div>
                <input type="file" id="fileInput" accept=".pdf" multiple>
            </div>
            <div id="uploadStatus"></div>
        </div>
        
        <!-- Query Section -->
        <div class="section">
            <h2>❓ Ask Questions</h2>
            <form id="queryForm">
                <input type="text" 
                       id="queryInput" 
                       class="query-input" 
                       placeholder="Ask a question about your uploaded documents..."
                       required>
                <button type="submit" class="btn">Search with Citations</button>
            </form>
            
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <div>Processing your query...</div>
            </div>
            
            <div id="results"></div>
        </div>
    </div>

    <script>
        // File upload handling
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.querySelector('.upload-area');
        const uploadStatus = document.getElementById('uploadStatus');
        
        fileInput.addEventListener('change', handleFileUpload);
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileUpload();
            }
        });
        
        async function handleFileUpload() {
            const files = fileInput.files;
            if (files.length === 0) return;
            
            uploadStatus.innerHTML = '<div class="loading"><div class="spinner"></div>Uploading and processing documents...</div>';
            
            for (let file of files) {
                if (!file.name.toLowerCase().endsWith('.pdf')) {
                    showAlert('Only PDF files are supported', 'error');
                    continue;
                }
                
                const formData = new FormData();
                formData.append('file', file);
                
                try {
                    const response = await fetch('/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        showAlert(`✅ ${result.message}`, 'success');
                    } else {
                        showAlert(`❌ Error: ${result.detail}`, 'error');
                    }
                } catch (error) {
                    showAlert(`❌ Upload failed: ${error.message}`, 'error');
                }
            }
            
            uploadStatus.innerHTML = '';
            fileInput.value = '';
        }
        
        // Query handling
        const queryForm = document.getElementById('queryForm');
        const queryInput = document.getElementById('queryInput');
        const loading = document.getElementById('loading');
        const results = document.getElementById('results');
        
        queryForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const query = queryInput.value.trim();
            if (!query) return;
            
            loading.style.display = 'block';
            results.innerHTML = '';
            
            try {
                const formData = new FormData();
                formData.append('query', query);
                
                const response = await fetch('/query', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResults(result);
                } else {
                    showAlert(`❌ Query failed: ${result.detail}`, 'error');
                }
            } catch (error) {
                showAlert(`❌ Query failed: ${error.message}`, 'error');
            } finally {
                loading.style.display = 'none';
            }
        });
        
        function displayResults(result) {
            if (!result.answer) {
                results.innerHTML = '<div class="alert alert-error">No answer generated</div>';
                return;
            }
            
            let html = '<div class="result-container">';
            html += '<h3>📝 Answer</h3>';
            
            // Process the answer text with citations
            let answerHtml = result.answer;
            
            // Replace citation markers with interactive elements
            if (result.citations && result.citations.length > 0) {
                result.citations.forEach((citation, index) => {
                    const citationId = `citation-${index}`;
                    const snippet = citation.answer_snippet;
                    const tooltipContent = `
                        <strong>Source:</strong> Page ${citation.page_number}<br>
                        <strong>Sentences:</strong> ${citation.sentences_range}<br>
                        <strong>Text:</strong> ${citation.chunk_sentences_text}
                    `;
                    
                    answerHtml = answerHtml.replace(
                        snippet,
                        `<span class="citation" data-tooltip="${tooltipContent.replace(/"/g, '&quot;')}">${snippet}</span>`
                    );
                });
            }
            
            html += `<div class="answer">${answerHtml}</div>`;
            
            // Add sources section
            if (result.citations && result.citations.length > 0) {
                html += '<div class="sources">';
                html += '<h4>📚 Sources</h4>';
                
                result.citations.forEach((citation, index) => {
                    html += `
                        <div class="source-item">
                            <strong>[${index + 1}] Page ${citation.page_number}</strong><br>
                            <em>Sentences ${citation.sentences_range}:</em><br>
                            "${citation.chunk_sentences_text}"
                        </div>
                    `;
                });
                
                html += '</div>';
            }
            
            html += '</div>';
            results.innerHTML = html;
            
            // Add tooltip functionality
            addTooltipListeners();
        }
        
        function addTooltipListeners() {
            const citations = document.querySelectorAll('.citation');
            
            citations.forEach(citation => {
                citation.addEventListener('mouseenter', (e) => {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'tooltip';
                    tooltip.innerHTML = e.target.dataset.tooltip;
                    tooltip.style.display = 'block';
                    
                    document.body.appendChild(tooltip);
                    
                    const rect = e.target.getBoundingClientRect();
                    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                    tooltip.style.top = rect.bottom + 10 + 'px';
                    
                    e.target._tooltip = tooltip;
                });
                
                citation.addEventListener('mouseleave', (e) => {
                    if (e.target._tooltip) {
                        e.target._tooltip.remove();
                        delete e.target._tooltip;
                    }
                });
            });
        }
        
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = message;
            
            uploadStatus.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
