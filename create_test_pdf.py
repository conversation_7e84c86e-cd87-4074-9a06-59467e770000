#!/usr/bin/env python3
"""
Create a simple test PDF file for testing the upload functionality
"""

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

import os

def create_test_pdf_with_reportlab():
    """Create a test PDF using reportlab"""
    filename = "test_document.pdf"
    
    # Create a simple PDF
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Page 1
    c.drawString(100, height - 100, "Test Document for Citation RAG System")
    c.drawString(100, height - 140, "")
    c.drawString(100, height - 180, "This is the first page of our test document.")
    c.drawString(100, height - 220, "Artificial Intelligence has revolutionized many industries.")
    c.drawString(100, height - 260, "Machine learning algorithms can process vast amounts of data.")
    c.drawString(100, height - 300, "These systems are becoming increasingly sophisticated.")
    
    c.showPage()
    
    # Page 2
    c.drawString(100, height - 100, "Page 2: Deep Learning Applications")
    c.drawString(100, height - 140, "")
    c.drawString(100, height - 180, "Deep learning is a subset of machine learning.")
    c.drawString(100, height - 220, "Neural networks with multiple layers can learn complex patterns.")
    c.drawString(100, height - 260, "Computer vision and natural language processing have benefited greatly.")
    c.drawString(100, height - 300, "These technologies are transforming healthcare and finance.")
    
    c.showPage()
    
    # Page 3
    c.drawString(100, height - 100, "Page 3: Future Directions")
    c.drawString(100, height - 140, "")
    c.drawString(100, height - 180, "The future of AI includes explainable AI frameworks.")
    c.drawString(100, height - 220, "Quantum computing integration shows promising potential.")
    c.drawString(100, height - 260, "Ethical AI considerations are becoming increasingly important.")
    c.drawString(100, height - 300, "Research continues to push the boundaries of what's possible.")
    
    c.save()
    
    print(f"✅ Test PDF created: {filename}")
    print(f"📊 File size: {os.path.getsize(filename)} bytes")
    return filename

def create_test_pdf_alternative():
    """Create a simple text file as alternative if reportlab is not available"""
    filename = "test_document.txt"
    
    content = """Test Document for Citation RAG System

Page 1:
This is the first page of our test document.
Artificial Intelligence has revolutionized many industries.
Machine learning algorithms can process vast amounts of data.
These systems are becoming increasingly sophisticated.

Page 2: Deep Learning Applications
Deep learning is a subset of machine learning.
Neural networks with multiple layers can learn complex patterns.
Computer vision and natural language processing have benefited greatly.
These technologies are transforming healthcare and finance.

Page 3: Future Directions
The future of AI includes explainable AI frameworks.
Quantum computing integration shows promising potential.
Ethical AI considerations are becoming increasingly important.
Research continues to push the boundaries of what's possible.
"""
    
    with open(filename, 'w') as f:
        f.write(content)
    
    print(f"✅ Test text file created: {filename}")
    print(f"📊 File size: {os.path.getsize(filename)} bytes")
    print("⚠️  Note: This is a text file, not a PDF. The system requires PDF files.")
    return filename

def main():
    """Create a test file for upload testing"""
    print("📄 Creating Test Document for Upload Testing")
    print("=" * 50)
    
    if REPORTLAB_AVAILABLE:
        print("📦 reportlab is available - creating PDF")
        filename = create_test_pdf_with_reportlab()
    else:
        print("❌ reportlab not available")
        print("💡 Installing reportlab for PDF creation...")
        try:
            import subprocess
            import sys
            subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
            print("✅ reportlab installed successfully")
            
            # Try again
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            filename = create_test_pdf_with_reportlab()
        except Exception as e:
            print(f"❌ Could not install reportlab: {e}")
            print("📝 Creating text file instead...")
            filename = create_test_pdf_alternative()
    
    print(f"\n🎯 Test file ready: {filename}")
    print("\nTo test the upload:")
    print("1. Start the web interface: python web_interface.py")
    print("2. Open http://localhost:8000 in your browser")
    print(f"3. Upload the file: {filename}")
    print("4. Check the server logs for detailed error information")

if __name__ == "__main__":
    main()
