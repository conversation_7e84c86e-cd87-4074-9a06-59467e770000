# 🗄️ SQLite3 in Python - Complete Guide

## ❌ The Error You Encountered

```bash
ERROR: No matching distribution found for sqlite3
```

This error occurs because **sqlite3 is part of Python's standard library** and doesn't need to be installed via pip!

## ✅ Correct Usage of SQLite3 in Python

### 1. Import Statement
SQLite3 is built into Python, so you simply import it:

```python
import sqlite3
```

**No installation required!** It's been part of Python's standard library since Python 2.5.

### 2. Basic Usage Pattern

```python
import sqlite3

# Connect to database (creates file if it doesn't exist)
conn = sqlite3.connect('database.db')

# Create a cursor object
cursor = conn.cursor()

# Execute SQL commands
cursor.execute('''
    CREATE TABLE IF NOT EXISTS documents (
        id INTEGER PRIMARY KEY,
        filename TEXT NOT NULL,
        content TEXT
    )
''')

# Insert data
cursor.execute("INSERT INTO documents (filename, content) VALUES (?, ?)", 
               ("example.pdf", "Document content here"))

# Commit changes
conn.commit()

# Query data
cursor.execute("SELECT * FROM documents")
results = cursor.fetchall()

# Close connection
conn.close()
```

### 3. Best Practices for SQLite in Python Applications

#### Use Context Managers
```python
import sqlite3

def setup_database():
    with sqlite3.connect('citations.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                total_pages INTEGER,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        # Automatically commits on successful exit
        # Automatically rolls back on exception
```

#### Use Parameterized Queries (Prevent SQL Injection)
```python
# ✅ CORRECT - Use parameterized queries
cursor.execute("SELECT * FROM documents WHERE filename = ?", (filename,))

# ❌ WRONG - Never use string formatting
cursor.execute(f"SELECT * FROM documents WHERE filename = '{filename}'")
```

#### Handle Exceptions Properly
```python
import sqlite3

try:
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM documents")
    results = cursor.fetchall()
except sqlite3.Error as e:
    print(f"Database error: {e}")
except Exception as e:
    print(f"Exception in _query: {e}")
finally:
    if conn:
        conn.close()
```

## 🔧 Fixed Requirements.txt

The corrected `requirements.txt` should NOT include these standard library modules:
- ❌ `sqlite3` (built into Python)
- ❌ `re` (built into Python)
- ❌ `json` (built into Python)
- ❌ `typing` (built into Python)
- ❌ `pathlib` (built into Python)
- ❌ `datetime` (built into Python)

**Correct requirements.txt:**
```
fastapi==0.115.9
uvicorn[standard]>=0.18.3
kdbai_client
PyPDF2
python-multipart
jinja2
openai
pandas
numpy
sentence-transformers
python-dotenv
```

## 🏗️ SQLite3 in Our Citation System

Our citation system uses SQLite3 correctly. Here's how it's implemented:

### Database Setup
```python
import sqlite3
import json
from pathlib import Path

class CitationRAGSystem:
    def setup_database(self):
        """Setup SQLite database for document metadata"""
        self.db_path = "citations.db"
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                total_pages INTEGER,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
```

### Adding Documents
```python
def add_document_metadata(self, document_id, doc_data, file_path):
    """Store document metadata in SQLite"""
    conn = sqlite3.connect(self.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO documents (id, filename, file_path, total_pages, metadata)
        VALUES (?, ?, ?, ?, ?)
    ''', (
        document_id,
        doc_data['filename'],
        file_path,
        doc_data['total_pages'],
        json.dumps(doc_data['metadata'])
    ))
    
    conn.commit()
    conn.close()
```

### Querying Documents
```python
def get_document_info(self, document_id):
    """Retrieve document information from SQLite"""
    conn = sqlite3.connect(self.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT filename, file_path, total_pages, metadata, created_at
        FROM documents WHERE id = ?
    ''', (document_id,))
    
    result = cursor.fetchone()
    conn.close()
    
    if result:
        return {
            'filename': result[0],
            'file_path': result[1],
            'total_pages': result[2],
            'metadata': json.loads(result[3]),
            'created_at': result[4]
        }
    return None
```

## 🚀 Installation Instructions (Fixed)

1. **Install only external dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **SQLite3 is automatically available:**
   ```python
   import sqlite3  # This just works!
   ```

3. **Test SQLite3 availability:**
   ```python
   import sqlite3
   print(f"SQLite version: {sqlite3.sqlite_version}")
   print(f"SQLite3 module version: {sqlite3.version}")
   ```

## 🔍 Common SQLite3 Patterns in Python

### In-Memory Database (for testing)
```python
conn = sqlite3.connect(':memory:')
```

### Row Factory (get dict-like results)
```python
import sqlite3

def dict_factory(cursor, row):
    d = {}
    for idx, col in enumerate(cursor.description):
        d[col[0]] = row[idx]
    return d

conn = sqlite3.connect('database.db')
conn.row_factory = dict_factory
```

### Using sqlite3.Row (built-in row factory)
```python
import sqlite3

conn = sqlite3.connect('database.db')
conn.row_factory = sqlite3.Row

cursor = conn.cursor()
cursor.execute("SELECT * FROM documents")
row = cursor.fetchone()

# Access by column name
print(row['filename'])
# Access by index
print(row[0])
```

## 📚 Key Takeaways

1. **SQLite3 is built into Python** - never add it to requirements.txt
2. **Always use parameterized queries** to prevent SQL injection
3. **Use context managers** for automatic connection management
4. **Handle exceptions properly** with try/except blocks
5. **Consider using row factories** for more convenient data access

## 🎯 Next Steps

1. Remove `sqlite3` and other standard library modules from requirements.txt
2. Install the corrected dependencies: `pip install -r requirements.txt`
3. Test the citation system: `python demo_citation_parsing.py`
4. Run the full system: `python run_citation_system.py`

The SQLite3 functionality in our citation system is correctly implemented and will work perfectly once you install the actual external dependencies!
