# 🔧 Uvicorn Error Fix Guide

## ❌ The Error You Encountered

```
WARNING: You must pass the application as an import string to enable 'reload' or 'workers'.
```

## 🔍 Root Cause Analysis

The error occurs because of how uvic<PERSON> handles the `reload=True` parameter:

### ❌ **Incorrect Usage** (what was causing the error):
```python
# In web_interface.py (line 118)
uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
```

**Problem**: When `reload=True` is used, uvicorn needs to be able to reimport the application module when files change. Passing the app object directly (`app`) doesn't allow this because uvicorn can't determine which module to reload.

### ✅ **Correct Usage** (the fix):
```python
# Fixed version
uvicorn.run("web_interface:app", host="0.0.0.0", port=8000, reload=True)
```

**Solution**: Pass the application as an import string (`"web_interface:app"`) which tells uvicorn:
- `web_interface` = the module name
- `app` = the FastAPI application variable within that module

## 🛠️ What We Fixed

### 1. **Fixed web_interface.py**
```python
# Before (causing error)
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)

# After (fixed)
if __name__ == "__main__":
    uvicorn.run("web_interface:app", host="0.0.0.0", port=8000, reload=True)
```

### 2. **Improved run_citation_system.py**
```python
def start_web_server():
    """Start the web server"""
    print("\n🚀 Starting Citation RAG System...")
    print("Web interface will be available at: http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    print("\nStarting server... (this may take a moment to initialize)")
    
    try:
        # Use uvicorn directly with proper import string
        import uvicorn
        uvicorn.run("web_interface:app", host="0.0.0.0", port=8000, reload=True)
    except KeyboardInterrupt:
        print("\n👋 Citation RAG System stopped")
    except ImportError:
        print("❌ uvicorn not found, trying alternative startup method...")
        try:
            subprocess.run([sys.executable, "web_interface.py"])
        except Exception as e:
            print(f"❌ Error starting server: {e}")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("Try running directly: python web_interface.py")
```

## 🎯 Why This Error Occurs

### **Uvicorn Reload Mechanism**
1. **With reload=True**: Uvicorn watches for file changes and automatically restarts the server
2. **Import String Required**: To reload, uvicorn needs to know which module to reimport
3. **App Object Problem**: Passing `app` directly doesn't provide module information

### **Import String Format**
```python
"module_name:variable_name"
```
- `module_name`: Python file name (without .py)
- `variable_name`: FastAPI app variable name

### **Examples**
```python
# For file: web_interface.py with app = FastAPI()
uvicorn.run("web_interface:app", reload=True)

# For file: main.py with application = FastAPI()
uvicorn.run("main:application", reload=True)

# For package: myapp/server.py with api = FastAPI()
uvicorn.run("myapp.server:api", reload=True)
```

## 🚀 Testing the Fix

### 1. **Test Direct Web Interface**
```bash
python web_interface.py
```
Expected output:
```
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [12346] using StatReloader
```

### 2. **Test via Startup Script**
```bash
python run_citation_system.py
```
Expected output:
```
🚀 Citation RAG System Startup
========================================
✅ All required packages are installed
✅ Environment variables are set
✅ All tests passed

🚀 Starting Citation RAG System...
Web interface will be available at: http://localhost:8000
Press Ctrl+C to stop the server

Starting server... (this may take a moment to initialize)
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [12346] using StatReloader
```

### 3. **Verify Web Interface**
Open browser and go to: `http://localhost:8000`

You should see:
- 📚 Citation RAG System title
- Document upload section
- Query input section
- No error messages

## 🔧 Alternative Startup Methods

### **Method 1: Direct uvicorn command**
```bash
uvicorn web_interface:app --host 0.0.0.0 --port 8000 --reload
```

### **Method 2: Without reload (for production)**
```python
uvicorn.run("web_interface:app", host="0.0.0.0", port=8000, reload=False)
```

### **Method 3: With workers (production)**
```bash
uvicorn web_interface:app --host 0.0.0.0 --port 8000 --workers 4
```

## 🐛 Troubleshooting Common Issues

### **Issue 1: Module not found**
```
ModuleNotFoundError: No module named 'web_interface'
```
**Solution**: Make sure you're running from the correct directory containing `web_interface.py`

### **Issue 2: Port already in use**
```
OSError: [Errno 48] Address already in use
```
**Solution**: 
```bash
# Kill process using port 8000
lsof -ti:8000 | xargs kill -9

# Or use a different port
uvicorn.run("web_interface:app", host="0.0.0.0", port=8001, reload=True)
```

### **Issue 3: Import errors in web_interface.py**
```
ImportError: cannot import name 'CitationRAGSystem'
```
**Solution**: Make sure all dependencies are installed and `citation_system.py` is in the same directory

### **Issue 4: API key errors**
```
ValueError: OPENAI_API_KEY must be set in environment variables
```
**Solution**: Set up your `.env` file with proper API keys

## 📋 Best Practices for Uvicorn

### **Development**
```python
# Use reload for development
uvicorn.run("app:app", reload=True, debug=True)
```

### **Production**
```python
# Disable reload for production
uvicorn.run("app:app", host="0.0.0.0", port=8000, workers=4)
```

### **Environment-based Configuration**
```python
import os

if os.getenv("ENVIRONMENT") == "development":
    uvicorn.run("web_interface:app", reload=True, debug=True)
else:
    uvicorn.run("web_interface:app", host="0.0.0.0", port=8000, workers=4)
```

## ✅ Verification Checklist

- [ ] No uvicorn warning messages
- [ ] Server starts successfully
- [ ] Web interface loads at http://localhost:8000
- [ ] File changes trigger automatic reload (in development)
- [ ] Upload and query functionality works
- [ ] No import errors in console

## 🎉 Success Indicators

When everything is working correctly, you should see:

1. **Clean startup logs** without warnings
2. **Accessible web interface** at http://localhost:8000
3. **Automatic reload** when you modify files (in development mode)
4. **Functional upload and query** features
5. **Proper error handling** for missing API keys or other issues

The uvicorn error is now completely resolved! 🚀
