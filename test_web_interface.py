#!/usr/bin/env python3
"""
Test script to verify the web interface starts correctly
"""

import subprocess
import sys
import time
import requests
import signal
import os

def test_web_interface_startup():
    """Test that the web interface starts without errors"""
    print("🌐 Testing Web Interface Startup...")

    try:
        # Start the web interface in background
        process = subprocess.Popen(
            [sys.executable, "web_interface.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # Wait a moment for startup
        time.sleep(3)

        # Check if process is still running (not crashed)
        if process.poll() is None:
            print("✅ Web interface process started successfully")

            # Try to connect to the server
            try:
                response = requests.get("http://localhost:8000/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Web interface is responding to requests")
                    print(f"   Health check response: {response.json()}")
                    success = True
                else:
                    print(f"❌ Web interface returned status code: {response.status_code}")
                    success = False
            except requests.exceptions.RequestException as e:
                print(f"❌ Could not connect to web interface: {e}")
                success = False

            # Clean up - terminate the process
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()

            return success
        else:
            # Process crashed, get error output
            stdout, stderr = process.communicate()
            print("❌ Web interface process crashed")
            if stderr:
                print(f"   Error output: {stderr}")
            if stdout:
                print(f"   Standard output: {stdout}")
            return False

    except Exception as e:
        print(f"❌ Error testing web interface: {e}")
        return False

def test_uvicorn_import_string():
    """Test that uvicorn can import the app correctly"""
    print("\n🔍 Testing Uvicorn Import String...")

    try:
        # Test the import string directly
        import uvicorn
        from web_interface import app

        print("✅ Successfully imported FastAPI app")
        print(f"   App type: {type(app)}")
        print(f"   App routes: {len(app.routes)} routes defined")

        # Test that uvicorn can handle the import string
        # We won't actually run it, just validate the format
        import_string = "web_interface:app"
        module_name, app_name = import_string.split(":")

        # Try to import the module and get the app
        import importlib
        module = importlib.import_module(module_name)
        app_obj = getattr(module, app_name)

        print(f"✅ Import string '{import_string}' is valid")
        print(f"   Module: {module}")
        print(f"   App object: {app_obj}")

        return True

    except Exception as e:
        print(f"❌ Import string test failed: {e}")
        return False

def test_requirements_installed():
    """Test that all required packages are installed"""
    print("\n📦 Testing Required Packages...")

    required_packages = [
        'fastapi',
        'uvicorn',
        'jinja2',
        'multipart'  # python-multipart package imports as 'multipart'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)

    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    else:
        print("✅ All required packages are installed")
        return True

def main():
    """Run all web interface tests"""
    print("🌐 Web Interface Test Suite")
    print("=" * 50)

    # Test requirements first
    req_ok = test_requirements_installed()
    if not req_ok:
        print("\n❌ Cannot proceed without required packages")
        return

    # Test import string
    import_ok = test_uvicorn_import_string()

    # Test actual startup (only if import works)
    if import_ok:
        startup_ok = test_web_interface_startup()
    else:
        startup_ok = False

    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print(f"   Required packages: {'✅ OK' if req_ok else '❌ FAIL'}")
    print(f"   Import string: {'✅ OK' if import_ok else '❌ FAIL'}")
    print(f"   Web interface startup: {'✅ OK' if startup_ok else '❌ FAIL'}")

    if req_ok and import_ok and startup_ok:
        print("\n🎉 All tests passed!")
        print("✅ The uvicorn error has been fixed")
        print("✅ Web interface is working correctly")
        print("\nYou can now run:")
        print("  python web_interface.py")
        print("  or")
        print("  python run_citation_system.py")
        print("\nThen visit: http://localhost:8000")
    else:
        print("\n❌ Some tests failed")
        if not req_ok:
            print("   Fix: pip install -r requirements.txt")
        if not import_ok:
            print("   Fix: Check web_interface.py for syntax errors")
        if not startup_ok:
            print("   Fix: Check server startup logs for errors")

if __name__ == "__main__":
    main()
