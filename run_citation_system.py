#!/usr/bin/env python3
"""
Startup script for the Citation RAG System
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all requirements are installed"""
    try:
        import fastapi
        import uvicorn
        import kdbai_client
        import PyPDF2
        import openai
        import pandas
        import sentence_transformers
        from dotenv import load_dotenv
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_environment():
    """Check if environment variables are set"""
    from dotenv import load_dotenv
    load_dotenv()

    required_vars = ['OPENAI_API_KEY', 'KDBAI_ENDPOINT', 'KDBAI_API_KEY']
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please:")
        print("1. Copy .env.example to .env")
        print("2. Fill in your API keys in the .env file")
        return False
    else:
        print("✅ Environment variables are set")
        return True

def run_tests():
    """Run the test script"""
    print("\n🧪 Running system tests...")
    try:
        result = subprocess.run([sys.executable, "test_citation_system.py"],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def start_web_server():
    """Start the web server"""
    print("\n🚀 Starting Citation RAG System...")
    print("Web interface will be available at: http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    print("\nStarting server... (this may take a moment to initialize)")

    try:
        # Use uvicorn directly with proper import string
        import uvicorn
        uvicorn.run("web_interface:app", host="0.0.0.0", port=8000, reload=True)
    except KeyboardInterrupt:
        print("\n👋 Citation RAG System stopped")
    except ImportError:
        print("❌ uvicorn not found, trying alternative startup method...")
        try:
            subprocess.run([sys.executable, "web_interface.py"])
        except Exception as e:
            print(f"❌ Error starting server: {e}")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("Try running directly: python web_interface.py")

def main():
    """Main startup function"""
    print("🚀 Citation RAG System Startup")
    print("=" * 40)

    # Check requirements
    if not check_requirements():
        sys.exit(1)

    # Check environment
    if not check_environment():
        sys.exit(1)

    # Run tests
    if not run_tests():
        print("\n⚠️  Tests failed, but you can still try running the system")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)

    # Start the server
    start_web_server()

if __name__ == "__main__":
    main()
