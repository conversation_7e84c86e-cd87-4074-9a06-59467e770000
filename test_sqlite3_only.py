#!/usr/bin/env python3
"""
Test script to verify SQLite3 works correctly
This script only tests SQLite3 functionality and doesn't require external dependencies
"""

import os
import sqlite3
import json
import tempfile
from pathlib import Path

def test_sqlite3_basic():
    """Test basic SQLite3 functionality"""
    print("🔍 Testing Basic SQLite3 Functionality...")
    
    try:
        # Test in-memory database
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        
        # Create table
        cursor.execute('''
            CREATE TABLE test_documents (
                id TEXT PRIMARY KEY,
                filename TEXT NOT NULL,
                content TEXT,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert test data
        test_data = {
            'title': 'Test Document',
            'author': 'Test Author',
            'pages': 10
        }
        
        cursor.execute('''
            INSERT INTO test_documents (id, filename, content, metadata)
            VALUES (?, ?, ?, ?)
        ''', (
            'test_doc_1',
            'test.pdf',
            'This is test content for the document.',
            json.dumps(test_data)
        ))
        
        # Query data
        cursor.execute('SELECT * FROM test_documents WHERE id = ?', ('test_doc_1',))
        result = cursor.fetchone()
        
        conn.close()
        
        if result:
            print("✅ Basic SQLite3 operations work correctly")
            print(f"   Retrieved document: {result[1]}")
            print(f"   Metadata: {json.loads(result[3])}")
            return True
        else:
            print("❌ Failed to retrieve inserted data")
            return False
            
    except Exception as e:
        print(f"❌ SQLite3 error: {e}")
        return False

def test_sqlite3_file_database():
    """Test SQLite3 with file database"""
    print("\n🔍 Testing File-based SQLite3 Database...")
    
    try:
        # Create temporary database file
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            db_path = tmp_file.name
        
        # Connect to file database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create citations table (similar to our actual system)
        cursor.execute('''
            CREATE TABLE documents (
                id TEXT PRIMARY KEY,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                total_pages INTEGER,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert sample document
        cursor.execute('''
            INSERT INTO documents (id, filename, file_path, total_pages, metadata)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            'doc_123',
            'sample.pdf',
            '/path/to/sample.pdf',
            25,
            json.dumps({'title': 'Sample Document', 'author': 'John Doe'})
        ))
        
        conn.commit()
        conn.close()
        
        # Reopen database to verify persistence
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM documents')
        count = cursor.fetchone()[0]
        
        cursor.execute('SELECT filename, total_pages FROM documents WHERE id = ?', ('doc_123',))
        result = cursor.fetchone()
        
        conn.close()
        
        # Clean up
        os.unlink(db_path)
        
        if count == 1 and result:
            print("✅ File-based SQLite3 database works correctly")
            print(f"   Document count: {count}")
            print(f"   Retrieved: {result[0]} ({result[1]} pages)")
            return True
        else:
            print("❌ File database test failed")
            return False
            
    except Exception as e:
        print(f"❌ File database error: {e}")
        # Clean up on error
        try:
            if 'db_path' in locals():
                os.unlink(db_path)
        except:
            pass
        return False

def test_sqlite3_row_factory():
    """Test SQLite3 with row factory for dict-like access"""
    print("\n🔍 Testing SQLite3 Row Factory...")
    
    try:
        conn = sqlite3.connect(':memory:')
        
        # Set row factory to get dict-like access
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE citations (
                id INTEGER PRIMARY KEY,
                chunk_id TEXT,
                page_number INTEGER,
                text TEXT,
                citation_type TEXT
            )
        ''')
        
        cursor.execute('''
            INSERT INTO citations (chunk_id, page_number, text, citation_type)
            VALUES (?, ?, ?, ?)
        ''', ('chunk_001', 5, 'This is cited text from page 5', 'direct_quote'))
        
        cursor.execute('SELECT * FROM citations WHERE chunk_id = ?', ('chunk_001',))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            # Test both index and name access
            page_by_index = row[2]  # page_number by index
            page_by_name = row['page_number']  # page_number by name
            
            if page_by_index == page_by_name == 5:
                print("✅ SQLite3 Row factory works correctly")
                print(f"   Citation text: {row['text']}")
                print(f"   Page number: {row['page_number']}")
                return True
            else:
                print("❌ Row factory access failed")
                return False
        else:
            print("❌ No data retrieved with row factory")
            return False
            
    except Exception as e:
        print(f"❌ Row factory error: {e}")
        return False

def test_sqlite3_version_info():
    """Display SQLite3 version information"""
    print("\n📋 SQLite3 Version Information:")
    print(f"   SQLite library version: {sqlite3.sqlite_version}")
    print(f"   SQLite library version info: {sqlite3.sqlite_version_info}")
    print(f"   Python sqlite3 module API level: {sqlite3.apilevel}")
    print(f"   Parameter style: {sqlite3.paramstyle}")
    print(f"   Thread safety level: {sqlite3.threadsafety}")

def main():
    """Run all SQLite3 tests"""
    print("🗄️ SQLite3 Functionality Test Suite")
    print("=" * 50)
    print("This verifies that SQLite3 works correctly for our citation system\n")
    
    # Run tests
    test1 = test_sqlite3_basic()
    test2 = test_sqlite3_file_database()
    test3 = test_sqlite3_row_factory()
    
    # Display version info
    test_sqlite3_version_info()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print(f"   Basic operations: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   File database: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"   Row factory: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    all_passed = test1 and test2 and test3
    
    if all_passed:
        print("\n🎉 All SQLite3 tests passed!")
        print("✅ SQLite3 is working correctly for the citation system")
        print("\nNext steps:")
        print("1. Install external dependencies: pip install -r requirements.txt")
        print("2. Set up your API keys in .env file")
        print("3. Run the full system: python run_citation_system.py")
    else:
        print("\n❌ Some SQLite3 tests failed!")
        print("This indicates a problem with your Python installation.")
        print("SQLite3 should be built into Python by default.")

if __name__ == "__main__":
    main()
