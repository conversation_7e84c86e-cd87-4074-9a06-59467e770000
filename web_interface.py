#!/usr/bin/env python3
"""
FastAPI Web Interface for Citation RAG System
"""

import os
import json
import tempfile
import traceback
import logging
from pathlib import Path
from typing import List, Dict, Any

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
import uvicorn

from citation_system import CitationRAGSystem

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(title="Citation RAG System", description="Anthropic-style citations for document Q&A")

# Setup templates and static files
templates = Jinja2Templates(directory="templates")

# Initialize the RAG system
try:
    logger.info("Initializing Citation RAG System...")
    rag_system = CitationRAGSystem()
    logger.info("✅ Citation RAG System initialized successfully")
except Exception as e:
    logger.error(f"❌ Could not initialize RAG system: {e}")
    logger.error(f"Full traceback: {traceback.format_exc()}")
    rag_system = None

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page with document upload and query interface"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/upload")
async def upload_document(file: UploadFile = File(...)):
    """Upload and process a PDF document"""
    logger.info(f"📄 Upload request received for file: {file.filename}")

    if not rag_system:
        logger.error("❌ RAG system not initialized")
        raise HTTPException(status_code=500, detail="RAG system not initialized")

    if not file.filename.lower().endswith('.pdf'):
        logger.warning(f"❌ Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="Only PDF files are supported")

    tmp_file_path = None
    try:
        logger.info(f"💾 Saving temporary file for: {file.filename}")

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name

        logger.info(f"✅ Temporary file saved: {tmp_file_path}")
        logger.info(f"📊 File size: {len(content)} bytes")

        # Process the document
        logger.info(f"🔄 Processing document: {file.filename}")
        document_id = rag_system.add_document(tmp_file_path)
        logger.info(f"✅ Document processed successfully. ID: {document_id}")

        # Clean up temporary file
        os.unlink(tmp_file_path)
        logger.info(f"🗑️ Temporary file cleaned up: {tmp_file_path}")

        return JSONResponse({
            "status": "success",
            "document_id": document_id,
            "filename": file.filename,
            "message": f"Document '{file.filename}' uploaded and processed successfully"
        })

    except Exception as e:
        logger.error(f"❌ Error processing document {file.filename}: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Clean up temporary file if it exists
        if tmp_file_path and os.path.exists(tmp_file_path):
            try:
                os.unlink(tmp_file_path)
                logger.info(f"🗑️ Cleaned up temporary file after error: {tmp_file_path}")
            except Exception as cleanup_error:
                logger.error(f"❌ Failed to cleanup temporary file: {cleanup_error}")

        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@app.post("/query")
async def query_documents(query: str = Form(...)):
    """Query the documents and get response with citations"""
    if not rag_system:
        raise HTTPException(status_code=500, detail="RAG system not initialized")

    if not query.strip():
        raise HTTPException(status_code=400, detail="Query cannot be empty")

    try:
        result = rag_system.query_with_citations(query)
        return JSONResponse(result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@app.get("/api/query")
async def api_query(q: str, k: int = 5):
    """API endpoint for querying documents"""
    if not rag_system:
        raise HTTPException(status_code=500, detail="RAG system not initialized")

    if not q.strip():
        raise HTTPException(status_code=400, detail="Query parameter 'q' cannot be empty")

    try:
        result = rag_system.query_with_citations(q, k)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "rag_system_initialized": rag_system is not None
    }

if __name__ == "__main__":
    uvicorn.run("web_interface:app", host="0.0.0.0", port=8000, reload=True)
