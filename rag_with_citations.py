import os
from typing import List, Dict, Any, Sequence
from langchain_openai import OpenAIEmbeddings, OpenAI
from langchain_community.vectorstores import Chroma
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import TextLoader
from langchain.chains import RetrievalQA
from langchain_core.documents import Document
from langchain.retrievers import ContextualCompressionRetriever
from langchain.retrievers.document_compressors import LLMChainExtractor

# Set up API key
os.environ["OPENAI_API_KEY"] = ""

# 1. Document Loading and Processing
def load_and_process_documents(file_path: str) -> List[Document]:
    """Load and split documents into chunks with accurate line numbers and character positions."""
    # Read the file content
    with open(file_path, 'r', encoding='utf-8') as f:
        full_text = f.read()
    
    # Split text into lines while keeping line endings
    lines = full_text.splitlines(keepends=True)
    
    # Create a mapping of character positions to line numbers
    char_to_line = {}
    current_char = 0
    for line_num, line in enumerate(lines, 1):
        for _ in range(len(line)):
            char_to_line[current_char] = line_num
            current_char += 1
    
    # Load documents using TextLoader
    loader = TextLoader(file_path)
    raw_docs = loader.load()
    
    # Use smaller chunks for more precise retrieval
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=300,
        chunk_overlap=50,
        separators=["\n\n", "\n", ". ", " ", ""]
    )
    
    # Add metadata for line numbers and character positions
    docs = text_splitter.split_documents(raw_docs)
    
    for doc in docs:
        # Get the exact content and its position in the full text
        content = doc.page_content.strip()
        
        # Try to find the exact content in the original text
        start_char = full_text.find(content)
        
        if start_char == -1:  # If exact match not found, try with unstripped content
            start_char = full_text.find(doc.page_content)
            if start_char != -1:
                content = doc.page_content
        
        if start_char == -1:  # If still not found, use the first line as context
            doc.metadata['start_line'] = 1
            doc.metadata['end_line'] = 1
            doc.metadata['start_char'] = 0
            doc.metadata['end_char'] = min(100, len(full_text))
            doc.metadata['lines'] = full_text[:100] + '...' if len(full_text) > 100 else full_text
            continue
            
        end_char = start_char + len(content)
        
        # Find line numbers using the mapping
        start_line = char_to_line.get(start_char, 1)
        end_line = char_to_line.get(min(end_char - 1, len(full_text) - 1), len(lines))
        
        # Get the exact lines for context (2 lines before and after)
        context_lines = []
        context_start = max(1, start_line - 2)
        context_end = min(len(lines), end_line + 2)
        
        for i in range(context_start, context_end + 1):
            line_text = lines[i-1].strip('\n')
            context_lines.append(f"{i}: {line_text}\n")
        
        # Store the metadata
        doc.metadata['start_line'] = start_line
        doc.metadata['end_line'] = end_line
        doc.metadata['start_char'] = start_char
        doc.metadata['end_char'] = end_char
        doc.metadata['lines'] = ''.join(context_lines)
    
    return docs

# 2. Vector Store Setup
def setup_vector_store(docs: List[Document]):
    """Create and return a vector store with document embeddings."""
    embeddings = OpenAIEmbeddings()
    return Chroma.from_documents(docs, embeddings)

# 3. Enhanced Retriever with Contextual Compression
def get_enhanced_retriever(vectorstore, k: int = 4):
    """Create a retriever with contextual compression for better relevance."""
    base_retriever = vectorstore.as_retriever(search_kwargs={"k": k})
    
    # Use LLM to extract only the relevant parts of documents
    compressor = LLMChainExtractor.from_llm(OpenAI(temperature=0))
    
    # Create the contextual compression retriever
    compression_retriever = ContextualCompressionRetriever(
        base_compressor=compressor,
        base_retriever=base_retriever
    )
    
    return compression_retriever

# 4. Process Query and Get Results
def process_query(query: str, retriever, k: int = 3) -> Dict[str, Any]:
    """Process a query and return relevant documents with scores."""
    # First get the most relevant documents
    relevant_docs = retriever.invoke(query)
    
    # If no relevant documents found, return empty result
    if not relevant_docs:
        return {"answer": "No relevant information found.", "sources": []}
    
    # Use LLM to generate an answer based on the retrieved documents
    llm = OpenAI(temperature=0)
    qa_chain = RetrievalQA.from_chain_type(
        llm=llm,
        chain_type="stuff",
        retriever=retriever,
        return_source_documents=True
    )
    
    result = qa_chain.invoke({"query": query})
    
    # Process sources to remove duplicates and irrelevant information
    unique_sources = []
    seen_content = set()
    
    # Extract key terms from the query for filtering
    query_terms = set(query.lower().split())
    # Remove common stop words that don't help with filtering
    stop_words = {"what", "is", "the", "of", "in", "to", "for", "with", "on", "at", "from"}
    query_terms = {term for term in query_terms if term not in stop_words and len(term) > 2}
    
    for doc in result["source_documents"]:
        doc_content = doc.page_content.lower()
        # Include source if it contains any of the query terms or if no specific terms were found
        if not query_terms or any(term in doc_content for term in query_terms):
            # Clean up the content
            content = doc.page_content.strip()
            if content and content not in seen_content:
                seen_content.add(content)
                # Get line numbers and character positions
                start_line = doc.metadata.get('start_line', 1)
                end_line = doc.metadata.get('end_line', start_line)
                start_char = doc.metadata.get('start_char', 0)
                end_char = doc.metadata.get('end_char', start_char + len(content))
                
                unique_sources.append({
                    "content": content,
                    "metadata": {
                        "start_line": start_line,
                        "end_line": end_line,
                        "start_char": start_char,
                        "end_char": end_char,
                        "file_path": doc.metadata.get('source', 'unknown'),
                        "lines": doc.metadata.get('lines', '').strip()
                    }
                })
    
    return {
        "answer": result["result"],
        "sources": unique_sources
    }

# Main execution
def main():
    import argparse
    
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Query your documents using RAG')
    parser.add_argument('query', nargs='?', default=None, 
                      help='The question you want to ask (enclose in quotes if it contains spaces)')
    args = parser.parse_args()
    
    # Load and process documents
    print("Loading and processing documents...")
    docs = load_and_process_documents("example_docs.txt")
    
    # Set up vector store
    print("Setting up vector store...")
    vectorstore = setup_vector_store(docs)
    
    # Get enhanced retriever
    print("Initializing retriever...")
    retriever = get_enhanced_retriever(vectorstore)
    
    # Get query from command line or prompt
    if args.query:
        query = args.query
    else:
        query = input("\nEnter your question: ")
    
    if not query.strip():
        query = "What is the meaning of kwaci?"  # Default query if none provided
    
    print(f"\nProcessing query: '{query}'")
    
    # Get results
    result = process_query(query, retriever)
    
    # Print results
    print("\nAnswer:", result["answer"])
    
    if result["sources"]:
        print("\nRelevant Sources:")
        for i, source in enumerate(result["sources"][:3], 1):  # Limit to top 3 sources
            metadata = source['metadata']
            print(f"\n[{i}] File: {metadata['file_path']}")
            print(f"   Lines: {metadata['start_line']}-{metadata['end_line']}")
            print(f"   Character range: {metadata['start_char']}-{metadata['end_char']}")
            print(f"   Content: {source['content'].strip()}")
            print("\n   Context:")
            print(metadata['lines'].rstrip())
    else:
        print("\nNo relevant sources found.")

if __name__ == "__main__":
    main()