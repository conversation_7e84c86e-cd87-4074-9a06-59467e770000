#!/usr/bin/env python3
"""
Anthropic-Style Citation System for RAG
Based on the Medium article: https://medium.com/data-science-collective/anthropic-style-citations-with-any-llm-2c061671ddd5
"""

import os
import re
import json
import sqlite3
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

import pandas as pd
import PyPDF2
import kdbai_client as kdbai
from sentence_transformers import SentenceTransformer
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class DocumentProcessor:
    """Handles PDF document processing and text extraction"""

    def __init__(self):
        self.supported_formats = ['.pdf', '.txt']

    def extract_text_from_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """Extract text from PDF with page-level granularity"""
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            pages = []

            # Extract metadata while file is open
            metadata = {
                'title': '',
                'author': '',
                'creation_date': datetime.now().isoformat()
            }

            try:
                if pdf_reader.metadata:
                    metadata['title'] = getattr(pdf_reader.metadata, 'title', '') or ''
                    metadata['author'] = getattr(pdf_reader.metadata, 'author', '') or ''
            except Exception as e:
                # If metadata extraction fails, continue with empty metadata
                print(f"Warning: Could not extract PDF metadata: {e}")

            # Extract text from all pages
            for page_num, page in enumerate(pdf_reader.pages, 1):
                text = page.extract_text()
                pages.append({
                    'page_number': page_num,
                    'text': text,
                    'char_count': len(text)
                })

        return {
            'filename': Path(pdf_path).name,
            'total_pages': len(pages),
            'pages': pages,
            'metadata': metadata
        }

    def parse_chunk_into_sentences(self, chunk_text: str) -> List[Dict[str, Any]]:
        """
        Splits chunk_text into sentences with start/end offsets.
        Based on the Medium article implementation.
        """
        # Simple regex to split on periods while capturing them
        raw_parts = re.split(r'(\.)', chunk_text)

        # Combine text + punctuation
        combined = []
        for i in range(0, len(raw_parts), 2):
            text_part = raw_parts[i].strip()
            punct = ""
            if i+1 < len(raw_parts):
                punct = raw_parts[i+1]
            if text_part or punct:
                combined_text = (text_part + punct).strip()
                if combined_text:
                    combined.append(combined_text)

        sentences = []
        offset = 0
        for s_id, s_txt in enumerate(combined, start=1):
            start_char = offset
            end_char = start_char + len(s_txt)
            sentences.append({
                "sentence_id": s_id,
                "text": s_txt,
                "start_char": start_char,
                "end_char": end_char
            })
            offset = end_char + 1  # assume space after each
        return sentences

class KDBAIVectorStore:
    """Handles KDB.AI vector database operations"""

    def __init__(self):
        self.session = None
        self.database = None
        self.table = None
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.setup_connection()

    def setup_connection(self):
        """Setup KDB.AI connection"""
        endpoint = os.getenv('KDBAI_ENDPOINT')
        api_key = os.getenv('KDBAI_API_KEY')

        if not endpoint or not api_key:
            raise ValueError("KDBAI_ENDPOINT and KDBAI_API_KEY must be set in environment variables")

        self.session = kdbai.Session(api_key=api_key, endpoint=endpoint)
        self.database = self.session.database("default")
        self.setup_table()

    def setup_table(self):
        """Create or get the citations table"""
        table_name = "citations_rag"

        # Drop existing table if it exists
        try:
            self.database.table(table_name).drop()
            time.sleep(2)  # Wait for cleanup
        except kdbai.KDBAIException:
            pass

        # Define schema for citations
        schema = [
            {"name": "document_id", "type": "str"},
            {"name": "chunk_id", "type": "str"},
            {"name": "page_number", "type": "int64"},
            {"name": "text", "type": "bytes"},
            {"name": "embeddings", "type": "float32s"},
            {"name": "sentences_data", "type": "bytes"},  # JSON string of sentence data
            {"name": "created_at", "type": "datetime64[ns]"}
        ]

        # Define vector index
        indexes = [{
            "name": "vector_index",
            "type": "flat",
            "column": "embeddings",
            "params": {"dims": 384, "metric": "L2"}
        }]

        self.table = self.database.create_table(table_name, schema=schema, indexes=indexes)

    def add_document_chunks(self, document_data: Dict[str, Any], chunk_size: int = 500, chunk_overlap: int = 100):
        """Add document chunks to vector store"""
        document_id = f"{document_data['filename']}_{int(time.time())}"
        records = []

        for page in document_data['pages']:
            page_text = page['text']
            if not page_text.strip():
                continue

            # Split page into chunks
            chunks = self._split_text_into_chunks(page_text, chunk_size, chunk_overlap)

            for chunk_idx, chunk_text in enumerate(chunks):
                # Parse chunk into sentences
                sentences_data = DocumentProcessor().parse_chunk_into_sentences(chunk_text)

                # Generate embedding
                embedding = self.embedding_model.encode(chunk_text).tolist()

                # Create record
                record = {
                    "document_id": document_id,
                    "chunk_id": f"{document_id}_page_{page['page_number']}_chunk_{chunk_idx}",
                    "page_number": page['page_number'],
                    "text": chunk_text.encode('utf-8'),
                    "embeddings": embedding,
                    "sentences_data": json.dumps(sentences_data).encode('utf-8'),
                    "created_at": pd.Timestamp.now()
                }
                records.append(record)

        # Insert records
        if records:
            df = pd.DataFrame(records)
            self.table.insert(df)
            return document_id
        return None

    def _split_text_into_chunks(self, text: str, chunk_size: int, chunk_overlap: int) -> List[str]:
        """Split text into overlapping chunks"""
        words = text.split()
        chunks = []

        for i in range(0, len(words), chunk_size - chunk_overlap):
            chunk_words = words[i:i + chunk_size]
            chunk_text = ' '.join(chunk_words)
            chunks.append(chunk_text)

            if i + chunk_size >= len(words):
                break

        return chunks

    def search_similar_chunks(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar chunks"""
        query_embedding = self.embedding_model.encode(query).tolist()

        results = self.table.search(
            vectors={"vector_index": [query_embedding]},
            n=k
        )

        if not results or len(results) == 0:
            return []

        # Process results
        chunks = []
        for _, row in results[0].iterrows():
            chunk_data = {
                "chunk_id": row["chunk_id"],
                "document_id": row["document_id"],
                "page_number": row["page_number"],
                "text": row["text"].decode('utf-8') if isinstance(row["text"], bytes) else row["text"],
                "sentences_data": json.loads(row["sentences_data"].decode('utf-8')) if isinstance(row["sentences_data"], bytes) else json.loads(row["sentences_data"])
            }
            chunks.append(chunk_data)

        return chunks

class CitationGenerator:
    """Handles citation generation using OpenAI with special prompting"""

    def __init__(self):
        openai.api_key = os.getenv('OPENAI_API_KEY')
        if not openai.api_key:
            raise ValueError("OPENAI_API_KEY must be set in environment variables")

    def generate_response_with_citations(self, chunks: List[Dict[str, Any]], user_query: str) -> str:
        """
        Generate response with inline citations using <CIT> tags
        Based on the Medium article approach
        """
        system_prompt = (
            "You have a collection of chunks from documents, each chunk may have multiple sentences.\n"
            "Please write a single continuous answer to the user's question.\n"
            "When you reference or rely on a specific portion of a chunk, cite it as:\n"
            "  <CIT chunk_id='N' sentences='X-Y'>the snippet of your final answer</CIT>\n"
            "Where:\n"
            "  - N is the chunk index (0, 1, 2, etc.).\n"
            "  - X-Y is the range of sentence numbers within that chunk. Example: 'sentences=2-4'.\n"
            "  - The text inside <CIT> is part of your answer, not the original chunk text.\n"
            "  - Keep your answer minimal in whitespace. Do not add extra spaces or line breaks.\n"
            "  - Only add <CIT> tags around the key phrases of your answer that rely on some chunk.\n"
            "    E.g. 'The study found <CIT chunk_id='0' sentences='1-2'>significant improvements in accuracy</CIT>.'\n\n"
            "Remember: The text inside <CIT> is your final answer's snippet, not the chunk text itself.\n"
            "The user question is below."
        )

        # Format chunks for the prompt
        chunks_info = "\n\n".join(
            f"[Chunk {i}] {chunk['text']}" for i, chunk in enumerate(chunks)
        )

        messages = [
            {"role": "system", "content": system_prompt},
            {
                "role": "user",
                "content": f"{chunks_info}\n\nQuestion: {user_query}\n"
            }
        ]

        response = openai.chat.completions.create(
            model="gpt-4o",
            messages=messages,
            temperature=0.3,
            max_tokens=1024
        )

        return response.choices[0].message.content

class CitationParser:
    """Parses LLM responses to extract citation information"""

    def parse_response_with_citations(self, response_text: str, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Parse response to extract citations and create structured output
        Based on the Medium article implementation
        """
        pattern = re.compile(
            r'(.*?)<CIT\s+chunk_id=[\'"](\d+)[\'\"]\s+sentences=[\'"](\d+-\d+)[\'"]>(.*?)(?:</CIT>|(?=<CIT)|$)',
            re.DOTALL
        )

        final_text = ""
        citations = []
        idx = 0

        while True:
            match = pattern.search(response_text, idx)
            if not match:
                # Add leftover text
                leftover = response_text[idx:]
                final_text += leftover
                break

            text_before = match.group(1)
            chunk_id_str = match.group(2)
            sent_range = match.group(3)
            snippet = match.group(4)

            final_text += text_before

            start_in_answer = len(final_text)
            final_text += snippet
            end_in_answer = len(final_text)

            # Get chunk data
            chunk_idx = int(chunk_id_str)
            if chunk_idx < len(chunks):
                chunk_data = chunks[chunk_idx]
                sentences_data = chunk_data['sentences_data']

                # Parse sentence range
                try:
                    start_sent, end_sent = map(int, sent_range.split("-"))
                except:
                    start_sent, end_sent = 1, 1

                # Get relevant sentences
                relevant_sents = [s for s in sentences_data if start_sent <= s["sentence_id"] <= end_sent]

                if relevant_sents:
                    combined_text = " ".join(s["text"] for s in relevant_sents)
                    chunk_start_char = relevant_sents[0]["start_char"]
                    chunk_end_char = relevant_sents[-1]["end_char"]
                else:
                    combined_text = ""
                    chunk_start_char = -1
                    chunk_end_char = -1

                citation = {
                    "chunk_id": chunk_idx,
                    "chunk_document_id": chunk_data['document_id'],
                    "page_number": chunk_data['page_number'],
                    "sentences_range": sent_range,
                    "answer_snippet": snippet,
                    "answer_snippet_start": start_in_answer,
                    "answer_snippet_end": end_in_answer,
                    "chunk_sentences_text": combined_text,
                    "chunk_sentences_start": chunk_start_char,
                    "chunk_sentences_end": chunk_end_char
                }
                citations.append(citation)

            idx = match.end()

        return {
            "type": "text",
            "text": final_text,
            "citations": citations
        }

class CitationRAGSystem:
    """Main RAG system with citation capabilities"""

    def __init__(self):
        self.doc_processor = DocumentProcessor()
        self.vector_store = KDBAIVectorStore()
        self.citation_generator = CitationGenerator()
        self.citation_parser = CitationParser()
        self.setup_database()

    def setup_database(self):
        """Setup SQLite database for document metadata"""
        self.db_path = "citations.db"
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                total_pages INTEGER,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def add_document(self, file_path: str) -> str:
        """Add a document to the system"""
        if not Path(file_path).exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Extract text from document
        if file_path.lower().endswith('.pdf'):
            doc_data = self.doc_processor.extract_text_from_pdf(file_path)
        else:
            raise ValueError("Only PDF files are currently supported")

        # Add to vector store
        document_id = self.vector_store.add_document_chunks(doc_data)

        # Store metadata in SQLite
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO documents (id, filename, file_path, total_pages, metadata)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            document_id,
            doc_data['filename'],
            file_path,
            doc_data['total_pages'],
            json.dumps(doc_data['metadata'])
        ))

        conn.commit()
        conn.close()

        return document_id

    def query_with_citations(self, query: str, k: int = 5) -> Dict[str, Any]:
        """Query the system and get response with citations"""
        # Search for relevant chunks
        chunks = self.vector_store.search_similar_chunks(query, k)

        if not chunks:
            return {
                "answer": "No relevant information found.",
                "citations": [],
                "sources": []
            }

        # Generate response with citations
        response_text = self.citation_generator.generate_response_with_citations(chunks, query)

        # Parse citations
        parsed_response = self.citation_parser.parse_response_with_citations(response_text, chunks)

        return {
            "answer": parsed_response["text"],
            "citations": parsed_response["citations"],
            "raw_response": response_text,
            "source_chunks": chunks
        }

if __name__ == "__main__":
    # Example usage
    rag_system = CitationRAGSystem()

    # Add a document (you'll need to provide a PDF file)
    # document_id = rag_system.add_document("path/to/your/document.pdf")

    # Query the system
    # result = rag_system.query_with_citations("What is the main topic discussed?")
    # print(json.dumps(result, indent=2))
