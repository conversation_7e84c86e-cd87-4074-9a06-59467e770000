# 📚 Anthropic-Style Citation RAG System

A powerful Retrieval-Augmented Generation (RAG) system that implements Anthropic-style citations, allowing users to upload PDF documents and ask questions with precise, traceable references to source material.

## 🌟 Features

- **PDF Document Processing**: Upload and process PDF documents with text extraction
- **Anthropic-Style Citations**: Generate responses with numbered citations `[1]`, `[2]` that link to specific document sections
- **KDB.AI Vector Database**: High-performance vector storage and similarity search
- **Sentence-Level Granularity**: Citations reference specific sentences within documents
- **Interactive Web Interface**: User-friendly interface with drag-and-drop file upload
- **Real-time Citation Display**: Hover tooltips show source content for each citation
- **Page-Level References**: Citations include page numbers and exact text locations

## 🏗️ Architecture

The system implements the approach described in the Medium article ["Anthropic-Style Citations with Any LLM"](https://medium.com/data-science-collective/anthropic-style-citations-with-any-llm-2c061671ddd5):

1. **Document Processing**: PDFs are processed and split into chunks with sentence-level parsing
2. **Vector Storage**: Document chunks are embedded and stored in KDB.AI vector database
3. **Citation Generation**: LLM generates responses with inline `<CIT>` tags
4. **Citation Parsing**: Tags are parsed to create interactive references
5. **Source Mapping**: Citations are mapped back to original document locations

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- OpenAI API key
- KDB.AI account and API credentials

### Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

4. **Configure your .env file**:
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   KDBAI_ENDPOINT=your_kdbai_endpoint_here
   KDBAI_API_KEY=your_kdbai_api_key_here
   ```

### Getting KDB.AI Credentials

1. Sign up at [KDB.AI](https://kdb.ai)
2. Create a new instance
3. Get your endpoint URL and API key from the dashboard
4. KDB.AI offers a generous free tier with 4GB RAM

### Running the System

1. **Test the setup**:
   ```bash
   python test_citation_system.py
   ```

2. **Start the web interface**:
   ```bash
   python web_interface.py
   ```

3. **Open your browser** and go to: `http://localhost:8000`

## 📖 Usage

### Web Interface

1. **Upload Documents**: 
   - Drag and drop PDF files or click to browse
   - Documents are automatically processed and indexed

2. **Ask Questions**:
   - Type your question in the search box
   - Get answers with interactive citations
   - Hover over citations to see source content

### API Usage

You can also use the system programmatically:

```python
from citation_system import CitationRAGSystem

# Initialize the system
rag_system = CitationRAGSystem()

# Add a document
document_id = rag_system.add_document("path/to/your/document.pdf")

# Query with citations
result = rag_system.query_with_citations("What is the main topic?")

print(result["answer"])
for citation in result["citations"]:
    print(f"Citation: Page {citation['page_number']}, Sentences {citation['sentences_range']}")
```

### API Endpoints

- `POST /upload` - Upload PDF documents
- `POST /query` - Query documents with form data
- `GET /api/query?q=your_question` - Query documents with URL parameters
- `GET /health` - Health check

## 🔧 System Components

### 1. DocumentProcessor
- Extracts text from PDF files using PyPDF2
- Parses text into sentences with character offsets
- Maintains page-level organization

### 2. KDBAIVectorStore
- Manages KDB.AI vector database connection
- Stores document chunks with embeddings
- Performs similarity search for relevant content

### 3. CitationGenerator
- Uses OpenAI GPT-4 with special prompting
- Generates responses with inline `<CIT>` tags
- Maps citations to specific sentence ranges

### 4. CitationParser
- Parses LLM responses to extract citation tags
- Maps citations back to source documents
- Creates structured citation metadata

### 5. Web Interface
- FastAPI-based web server
- Interactive HTML interface with JavaScript
- Real-time citation tooltips and source display

## 📊 Citation Format

The system generates citations in this format:

```
The study found <CIT chunk_id='0' sentences='1-2'>significant improvements in accuracy</CIT> when using the new method.
```

Which gets parsed into:
- **Answer Text**: "The study found significant improvements in accuracy when using the new method."
- **Citation**: Links "significant improvements in accuracy" to Chunk 0, Sentences 1-2
- **Source Reference**: Shows original document page and exact text

## 🎯 Key Benefits

1. **Transparency**: Every claim is traceable to source material
2. **Precision**: Citations reference specific sentences, not entire documents
3. **User Experience**: Interactive tooltips make verification easy
4. **Scalability**: KDB.AI handles large document collections efficiently
5. **Flexibility**: Works with any LLM that supports structured prompting

## 🔍 Example Output

**Question**: "What are the main applications of AI?"

**Answer**: "AI has numerous applications including <CIT chunk_id='0' sentences='2-3'>natural language processing, computer vision, and robotics</CIT>. These technologies are <CIT chunk_id='1' sentences='1-2'>transforming healthcare, finance, and transportation</CIT>."

**Citations**:
- [1] Page 3, Sentences 2-3: "AI applications include natural language processing, computer vision, and robotics."
- [2] Page 4, Sentences 1-2: "These technologies are transforming healthcare, finance, and transportation sectors."

## 🛠️ Customization

### Adjusting Chunk Size
Modify the `chunk_size` parameter in `KDBAIVectorStore.add_document_chunks()`:

```python
# Smaller chunks for more precise citations
document_id = vector_store.add_document_chunks(doc_data, chunk_size=300, chunk_overlap=50)
```

### Changing Embedding Model
Update the embedding model in `KDBAIVectorStore.__init__()`:

```python
self.embedding_model = SentenceTransformer('your-preferred-model')
```

### Custom Citation Prompts
Modify the system prompt in `CitationGenerator.generate_response_with_citations()` to adjust citation behavior.

## 🚨 Troubleshooting

### Common Issues

1. **"RAG system not initialized"**
   - Check your environment variables are set correctly
   - Verify KDB.AI credentials and endpoint

2. **"Only PDF files are supported"**
   - Currently only PDF files are supported
   - Ensure uploaded files have .pdf extension

3. **Empty responses**
   - Make sure documents are uploaded and processed
   - Try more specific questions

4. **Citation parsing errors**
   - The LLM might not follow the citation format exactly
   - This is normal and the system handles partial citations gracefully

### Performance Tips

- Use smaller chunk sizes for more precise citations
- Increase the number of retrieved chunks (`k` parameter) for better context
- Consider using more powerful embedding models for better retrieval

## 🔮 Future Enhancements

1. **Multi-format Support**: Add support for Word documents, text files, web pages
2. **Advanced UI**: Implement side-by-side document viewer with highlighted citations
3. **Citation Confidence**: Add confidence scores for citation accuracy
4. **Batch Processing**: Support for bulk document uploads
5. **Export Features**: Export answers with citations to PDF or Word
6. **User Management**: Add user accounts and document organization
7. **API Rate Limiting**: Implement rate limiting for production use

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## 📞 Support

If you encounter any issues or have questions, please:
1. Check the troubleshooting section above
2. Review the test script output: `python test_citation_system.py`
3. Ensure all environment variables are correctly set

---

**Built with ❤️ using the approach from the Medium article on Anthropic-style citations**
